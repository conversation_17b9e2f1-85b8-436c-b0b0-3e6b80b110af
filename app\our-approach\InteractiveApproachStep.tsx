'use client';

import React from 'react';

interface InteractiveApproachStepProps {
  children: React.ReactNode;
  stepName: string;
}

const InteractiveApproachStep: React.FC<InteractiveApproachStepProps> = ({ 
  children, 
  stepName 
}) => {
  const handleStepClick = () => {
    console.log(`${stepName} clicked`);
    // We can add more interactive functionality here
    // Such as displaying more information, animations, etc.
  };

  return (
    <div onClick={handleStepClick} className="w-full">
      {children}
    </div>
  );
};

export default InteractiveApproachStep; 