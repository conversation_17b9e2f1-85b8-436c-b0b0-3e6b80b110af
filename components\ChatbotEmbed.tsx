'use client';

import React, { useState, useRef, useEffect } from 'react';

const ChatbotEmbed: React.FC = () => {
  const [isChatOpen, setIsChatOpen] = useState(false);
  const iframeRef = useRef<HTMLIFrameElement | null>(null);
  const [isClient, setIsClient] = useState(false);
  const [isIframeLoaded, setIsIframeLoaded] = useState(false);
  
  // Set isClient to true only after mounting on the client
  useEffect(() => {
    setIsClient(true);
    
    // Clean up function to ensure all iframes are removed when component unmounts
    return () => {
      // Find and remove any existing iframes with our specific URL
      const existingIframes = document.querySelectorAll(`iframe[src="${process.env.NEXT_PUBLIC_CHATBOT_URL || 'http://localhost:3001/'}"]`);
      existingIframes.forEach(iframe => {
        if (iframe.parentNode) {
          iframe.parentNode.removeChild(iframe);
        }
      });
    };
  }, []);

  // This useEffect handles iframe creation and preloading
  useEffect(() => {
    if (!isClient) return;
    
    // Remove any existing iframes first to prevent duplicates
    const existingIframes = document.querySelectorAll(`iframe[src="${process.env.NEXT_PUBLIC_CHATBOT_URL || 'http://localhost:3001/'}"]`);
    existingIframes.forEach(iframe => {
      if (iframe.parentNode) {
        iframe.parentNode.removeChild(iframe);
      }
    });
    
    // Create and pre-load iframe
    const iframe = document.createElement('iframe');
    iframe.src = process.env.NEXT_PUBLIC_CHATBOT_URL || 'http://localhost:3001/';
    iframe.style.position = 'fixed';
    iframe.style.bottom = '24px';
    iframe.style.right = '24px';
    iframe.style.width = '400px';
    iframe.style.height = '600px';
    iframe.style.border = 'none';//2px solid #7c3aed
    iframe.style.backgroundColor = 'transparent';
    iframe.style.zIndex = '9999';
    iframe.style.display = 'none';
    iframe.style.borderRadius = '16px'; // Add rounded corners to match the design
    iframe.style.maxWidth = '95vw'; // Ensure iframe doesn't exceed viewport width
    iframe.style.maxHeight = '85vh'; // Ensure iframe doesn't exceed viewport height
    
    // Add responsive behavior for smaller screens like iPhone SE
    function updateIframeSize() {
      const screenWidth = window.innerWidth;
      const screenHeight = window.innerHeight;
      
      if (screenWidth <= 375) { // iPhone SE width
        // For very small screens, use almost full width and adjust height
        iframe.style.width = 'calc(100vw - 20px)';  // Almost full width with small margin
        iframe.style.height = 'calc(80vh - 20px)';  // 80% of viewport height
        iframe.style.right = '10px';                // Small margin on right
        iframe.style.bottom = '80px';               // Give more space at bottom
        
        // Notify the iframe content about the small screen
        if (iframe.contentWindow && isIframeLoaded) {
          iframe.contentWindow.postMessage(
            { type: 'responsiveView', payload: { isSmallScreen: true, width: screenWidth, height: screenHeight } },
            process.env.NEXT_PUBLIC_CHATBOT_URL || 'http://localhost:3001'
          );
        }
      } else {
        // Default size for normal screens
        iframe.style.width = '400px';
        iframe.style.height = '600px';
        iframe.style.right = '24px';
        iframe.style.bottom = '24px';
        iframe.style.left = 'auto';
        
        // Notify the iframe content about the normal screen
        if (iframe.contentWindow && isIframeLoaded) {
          iframe.contentWindow.postMessage(
            { type: 'responsiveView', payload: { isSmallScreen: false, width: screenWidth, height: screenHeight } },
            process.env.NEXT_PUBLIC_CHATBOT_URL || 'http://localhost:3001'
          );
        }
      }
    }
    
    // Initialize size based on current viewport
    // We'll call this after iframe is loaded to ensure messaging works
    
    // Update iframe size when window is resized
    window.addEventListener('resize', updateIframeSize);
    
    // Optional: add shadow for better visual appearance
    iframe.allow = 'microphone';
    iframe.id = 'upzera-chatbot-iframe';
    
    // Store reference to iframe
    iframeRef.current = iframe;
    
    // Append to body
    document.body.appendChild(iframe);
    
    // Check if iframe is loaded
    iframe.onload = () => {
      console.log('Iframe loaded');
      setIsIframeLoaded(true);
      
      // Now that the iframe is loaded, set its initial size
      updateIframeSize();
      
      // If the user had already clicked the button before the iframe loaded,
      // we need to open the chat now
      if (isChatOpen && iframe.contentWindow) {
        iframe.style.display = 'block';
        iframe.contentWindow.postMessage(
          { type: 'toggleChat', payload: { isOpen: true } },
          process.env.NEXT_PUBLIC_CHATBOT_URL || 'http://localhost:3001'
        );
      }
    };
    
    // Clean up on unmount or when this effect runs again
    return () => {
      if (iframe && iframe.parentNode) {
        iframe.parentNode.removeChild(iframe);
      }
      window.removeEventListener('resize', updateIframeSize);
    };
  }, [isClient]); // Removed isChatOpen dependency to prevent recreation

  // Effect to handle chat toggle
  useEffect(() => {
    if (!isClient || !iframeRef.current) return;
    
    if (isChatOpen) {
      // First make the iframe visible
      iframeRef.current.style.display = 'block';
      
      // Then send the message immediately
      if (iframeRef.current.contentWindow && isIframeLoaded) {
        console.log('Sending message to open chat');
        iframeRef.current.contentWindow.postMessage(
          { type: 'toggleChat', payload: { isOpen: true } }, 
          process.env.NEXT_PUBLIC_CHATBOT_URL || 'http://localhost:3001'
        );
        
        // Try to focus the iframe
        try {
          iframeRef.current.focus();
        } catch (e) {
          console.error('Could not focus iframe:', e);
        }
      }
    } else {
      if (iframeRef.current && iframeRef.current.contentWindow && isIframeLoaded) {
        // Send close message first
        console.log('Sending message to close chat');
        iframeRef.current.contentWindow.postMessage(
          { type: 'toggleChat', payload: { isOpen: false } }, 
          process.env.NEXT_PUBLIC_CHATBOT_URL || 'http://localhost:3001'
        );
        
        // Hide immediately
        iframeRef.current.style.display = 'none';
      } else {
        // If iframe isn't loaded yet, just update the display
        if (iframeRef.current) {
          iframeRef.current.style.display = 'none';
        }
      }
    }
  }, [isChatOpen, isClient, isIframeLoaded]);

  // Listen for postMessage from iframe to hide iframe but keep widget button
  useEffect(() => {
    function handleMessage(event: MessageEvent) {
      if (event.data && event.data.type === 'closeChatbot') {
        setIsChatOpen(false);
      }
    }

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, []);


  const handleChatToggle = () => {
    console.log('Chat toggle clicked, current state:', isChatOpen);
    setIsChatOpen(!isChatOpen);
  };

  // Render nothing on the server or before hydration completes
  if (!isClient) {
    return null;
  }

  return (
    <>
      {/* Chat Trigger Button */}
      <button
        onClick={handleChatToggle}
        className={`fixed bottom-6 right-6 z-50 w-14 h-14 flex items-center justify-center rounded-full shadow-lg transition-transform hover:scale-105 ${
          !isChatOpen ? 'block' : 'hidden'
        }`}
        style={{
          background: 'linear-gradient(to bottom right, rgb(88, 28, 135), rgb(219, 39, 119))',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
        }}
        aria-label="Open Chat"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="white" strokeWidth={2}>
          <path strokeLinecap="round" strokeLinejoin="round" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
      </button>

      {/* Close Button - This appears floating over the iframe */}
    </>
  );
};

export default ChatbotEmbed;


