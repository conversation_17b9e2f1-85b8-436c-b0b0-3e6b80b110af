const FormData = require('form-data');
const Mailgun = require('mailgun.js');

// Email template utilities
function createContactFormNotificationEmail(data) {
  return `
    <div style="max-width:600px; margin:0 auto; font-family:'Arial', sans-serif; background:#ffffff; border:1px solid #e5e7eb; border-radius:12px; overflow:hidden; box-shadow:0 4px 6px rgba(0,0,0,0.05);">
      <!-- Header with Logo -->
      <div style="background: linear-gradient(135deg, #7e22ce, #9333ea); padding:28px; text-align:center; border-bottom:1px solid #a855f7;">
        <img src="https://upzera-web.netlify.app/logo/UpZera_logo_3-nobkgr.png" alt="UpZera Logo" style="max-height:70px; filter: brightness(0) invert(1);">
      </div>
      
      <!-- Content -->
      <div style="padding:32px;">
        <h2 style="color:#1f2937; font-size:24px; font-weight:600; margin:0 0 24px 0; text-align:center;">New Contact Form Submission</h2>
        
        <div style="background:#f9fafb; border-radius:8px; padding:20px; margin-bottom:24px;">
          <div style="margin-bottom:16px;">
            <strong style="color:#374151; font-size:14px;">Name:</strong>
            <p style="color:#1f2937; margin:4px 0 0 0; font-size:16px;">${data.name}</p>
          </div>
          
          <div style="margin-bottom:16px;">
            <strong style="color:#374151; font-size:14px;">Email:</strong>
            <p style="color:#1f2937; margin:4px 0 0 0; font-size:16px;">${data.email}</p>
          </div>
          
          <div style="margin-bottom:16px;">
            <strong style="color:#374151; font-size:14px;">Service:</strong>
            <p style="color:#1f2937; margin:4px 0 0 0; font-size:16px;">${data.service}</p>
          </div>
          
          <div>
            <strong style="color:#374151; font-size:14px;">Message:</strong>
            <p style="color:#1f2937; margin:4px 0 0 0; font-size:16px; line-height:1.5;">${data.message}</p>
          </div>
        </div>
      </div>
    </div>
  `;
}

function createContactFormConfirmationEmail(name) {
  return `
    <div style="max-width:600px; margin:0 auto; font-family:'Arial', sans-serif; background:#ffffff; border:1px solid #e5e7eb; border-radius:12px; overflow:hidden; box-shadow:0 4px 6px rgba(0,0,0,0.05);">
      <!-- Header with Logo -->
      <div style="background: linear-gradient(135deg, #7e22ce, #9333ea); padding:28px; text-align:center; border-bottom:1px solid #a855f7;">
        <img src="https://upzera-web.netlify.app/logo/UpZera_logo_3-nobkgr.png" alt="UpZera Logo" style="max-height:70px; filter: brightness(0) invert(1);">
      </div>
      
      <!-- Content -->
      <div style="padding:32px;">
        <h2 style="color:#1f2937; font-size:24px; font-weight:600; margin:0 0 24px 0; text-align:center;">Thank You for Contacting Us!</h2>
        
        <p style="color:#4b5563; font-size:16px; line-height:1.6; margin:0 0 20px 0;">
          Hi ${name},
        </p>
        
        <p style="color:#4b5563; font-size:16px; line-height:1.6; margin:0 0 20px 0;">
          Thank you for reaching out to UpZera! We've received your message and our team will get back to you within 24 hours.
        </p>
        
        <p style="color:#4b5563; font-size:16px; line-height:1.6; margin:0 0 20px 0;">
          In the meantime, feel free to explore our services and learn more about how we can help transform your business with AI solutions.
        </p>
        
        <div style="text-align:center; margin:32px 0;">
          <a href="https://upzera.com" style="background: linear-gradient(135deg, #7e22ce, #9333ea); color:white; padding:12px 24px; text-decoration:none; border-radius:8px; font-weight:600; display:inline-block;">
            Visit Our Website
          </a>
        </div>
        
        <p style="color:#6b7280; font-size:14px; line-height:1.5; margin:24px 0 0 0; text-align:center;">
          Best regards,<br>
          The UpZera Team
        </p>
      </div>
    </div>
  `;
}

exports.handler = async (event, context) => {
  // Handle CORS
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
  };

  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: '',
    };
  }

  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method not allowed' }),
    };
  }

  try {
    const { name, email, service, message } = JSON.parse(event.body);

    // Basic validation
    if (!name || !email || !service || !message) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'All fields are required' }),
      };
    }

    if (!/\S+@\S+\.\S+/.test(email)) {
      return {
        statusCode: 400,
        headers,
        body: JSON.stringify({ error: 'Valid email is required' }),
      };
    }

    // Initialize Mailgun
    const mailgun = new Mailgun(FormData);
    const mg = mailgun.client({
      username: 'api',
      key: process.env.MAILGUN_API_KEY,
      url: process.env.MAILGUN_REGION === 'eu' ? 'https://api.eu.mailgun.net' : 'https://api.mailgun.net'
    });

    // Create email content
    const notificationHtml = createContactFormNotificationEmail({
      name,
      email,
      service,
      message
    });

    const confirmationHtml = createContactFormConfirmationEmail(name);

    // Send notification email to admin team
    await mg.messages.create(process.env.MAILGUN_DOMAIN, {
      from: process.env.MAILGUN_FROM || 'UpZera <<EMAIL>>',
      to: ['<EMAIL>', '<EMAIL>'],
      subject: `New Contact Form Submission!! - ${service}`,
      html: notificationHtml
    });

    // Send confirmation email to client
    await mg.messages.create(process.env.MAILGUN_DOMAIN, {
      from: process.env.MAILGUN_FROM || 'UpZera <<EMAIL>>',
      to: email,
      subject: 'Thank you for contacting UpZera!',
      html: confirmationHtml
    });

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        message: 'Email sent successfully',
        success: true
      }),
    };
  } catch (error) {
    console.error('Error in contact function:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Failed to send email' }),
    };
  }
};
