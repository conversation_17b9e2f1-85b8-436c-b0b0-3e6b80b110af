"use client";

import React from 'react';
import Navbar from '@/components/navbar';

export default function NavbarTest3Page() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-purple-950 via-purple-900/95 to-purple-950 overflow-hidden">
      <Navbar />
      
      <div className="pt-28 pb-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
          <h1 className="text-4xl font-bold text-white mb-8 text-center">Navbar Test - Fixed Pink Underline</h1>
          <p className="text-xl text-white/80 mb-12 text-center">
            This page tests the navbar with the fixed pink underline for active menu items.
          </p>
          
          <div className="bg-purple-900/30 rounded-xl border border-purple-800/30 p-8 text-white">
            <h2 className="text-2xl font-semibold mb-4">Improvements Made:</h2>
            <ul className="list-disc list-inside space-y-2">
              <li>Added a more prominent pink underline (2px height)</li>
              <li>Added a subtle glow effect to the underline</li>
              <li>Added underline to Services dropdown when a service page is active</li>
              <li>Adjusted spacing between navbar items</li>
              <li>Improved z-index to ensure underline visibility</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
