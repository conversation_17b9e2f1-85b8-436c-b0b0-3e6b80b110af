# Mailgun Email Service Setup

This document explains how to set up and configure Mailgun for the UpZera email system.

## Overview

The UpZera project has been migrated from Zoho SMTP to Mailgun for better reliability and to avoid paid SMTP limitations. The system now uses Mailgun's API to send:

1. **Contact form notifications** to admin team (<EMAIL>, <EMAIL>)
2. **Contact form confirmations** to clients who submit the form
3. **Newsletter subscription notifications** to admin team
4. **Newsletter welcome emails** to subscribers

## Mailgun Configuration

### Required Environment Variables

Add these variables to your `.env` file:

```env
# Mailgun Configuration
MAILGUN_API_KEY=your_mailgun_api_key_here
MAILGUN_DOMAIN=mg.upzera.com
MAILGUN_FROM=UpZera <<EMAIL>>
MAILGUN_REGION=eu
```

### Getting Your Mailgun Credentials

1. **Sign up for Mailgun**: Go to [mailgun.com](https://www.mailgun.com/) and create an account
2. **Get your API Key**: 
   - Go to Settings > API Keys
   - Copy your Private API key
3. **Set up your domain**:
   - Add your domain (e.g., `mg.upzera.com`) in the Mailgun dashboard
   - Follow DNS verification steps
4. **Choose region**: Set to 'eu' if using European servers, 'us' for US servers

### Domain Setup

For production use, you'll need to:

1. **Add DNS records** for your domain:
   - TXT record for domain verification
   - MX records for receiving emails (optional)
   - CNAME records for tracking (optional)

2. **Verify domain** in Mailgun dashboard

## Email Templates

The system uses beautifully designed HTML email templates with:

- **UpZera branding** with logo and purple gradient design
- **Responsive layout** that works on all devices
- **Professional styling** consistent with your brand
- **Clear call-to-action buttons**

### Template Types

1. **Contact Form Notification** (`createContactFormNotificationEmail`)
   - Sent to admin team when someone submits contact form
   - Includes all form details (name, email, service, message)
   - Professional layout with contact details table

2. **Contact Form Confirmation** (`createContactFormConfirmationEmail`)
   - Sent to client who submitted the form
   - Confirms receipt and sets expectations for response time
   - Includes contact information for urgent matters

3. **Newsletter Notification** (`createNewsletterNotificationEmail`)
   - Sent to admin team when someone subscribes to newsletter
   - Shows subscriber email and subscription date

4. **Newsletter Welcome** (`createNewsletterConfirmationEmail`)
   - Sent to new newsletter subscribers
   - Welcomes them and explains what to expect
   - Includes unsubscribe instructions

## API Endpoints

### Contact Form API (`/api/contact`)

**Endpoint**: `POST /api/contact`

**Request Body**:
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "service": "Web Development",
  "message": "I need help with my website"
}
```

**Response**:
```json
{
  "message": "Email sent successfully",
  "success": true
}
```

**Features**:
- Input validation for all required fields
- Email format validation
- Sends notification to admin team
- Sends confirmation to client
- Proper error handling with detailed messages

### Newsletter API (`/api/newsletter`)

**Endpoint**: `POST /api/newsletter`

**Request Body**:
```json
{
  "email": "<EMAIL>"
}
```

**Response**:
```json
{
  "message": "Subscription successful",
  "success": true
}
```

**Features**:
- Email format validation
- Sends notification to admin team
- Sends welcome email to subscriber
- Proper error handling

## Error Handling

The system includes comprehensive error handling:

- **Validation errors**: Returns 400 status with specific error messages
- **Mailgun API errors**: Returns 500 status with error details
- **Configuration errors**: Throws descriptive errors for missing environment variables
- **Logging**: All email operations are logged for debugging

## Testing

To test the email functionality:

1. **Set up Mailgun account** and get credentials
2. **Update environment variables** with your Mailgun credentials
3. **Test contact form**: Submit a form on your website
4. **Test newsletter**: Subscribe to newsletter
5. **Check email delivery**: Verify emails are received by admin team and clients

## Migration from Zoho

The migration from Zoho SMTP to Mailgun includes:

- ✅ **Maintained all existing email templates** with improved design
- ✅ **Added client confirmation emails** for better user experience
- ✅ **Improved error handling** and logging
- ✅ **Better reliability** with Mailgun's infrastructure
- ✅ **Cost-effective** solution with generous free tier
- ✅ **Scalable** for future growth

## Troubleshooting

### Common Issues

1. **"Mailgun configuration is missing"**
   - Check that `MAILGUN_API_KEY` and `MAILGUN_DOMAIN` are set in `.env`
   - Verify environment variables are loaded correctly

2. **"Failed to send email"**
   - Check Mailgun API key is valid
   - Verify domain is verified in Mailgun dashboard
   - Check Mailgun logs in dashboard for specific errors

3. **Emails not being received**
   - Check spam folders
   - Verify recipient email addresses
   - Check Mailgun delivery logs

### Debug Mode

To enable debug logging, check the console output when emails are sent. The system logs:
- Email service configuration
- Email sending attempts
- Success/failure status
- Error details

## Security

- **API keys** are stored in environment variables, not in code
- **Input validation** prevents malicious data
- **Error messages** don't expose sensitive information
- **Rate limiting** should be implemented at the application level if needed

## Support

For Mailgun-specific issues:
- Check [Mailgun documentation](https://documentation.mailgun.com/)
- Contact Mailgun support
- Review Mailgun dashboard logs

For application issues:
- Check application logs
- Verify environment configuration
- Test with Mailgun's API testing tools
