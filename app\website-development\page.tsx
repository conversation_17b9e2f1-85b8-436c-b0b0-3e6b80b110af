"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { HeroBackground } from "@/components/ui/hero-background"; // Import the new component
import { Rocket, Zap, Cpu, LayoutTemplate, Search, Smartphone, Link2, CheckCircle } from "lucide-react";
import Link from "next/link";

export default function WebsiteDevelopment() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-950 via-purple-900 to-purple-950 pt-24">
    {/* Hero Section */}
    <div className="py-20 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="text-center">
          {/* Changed leading-tight to leading-normal */}
<h1 className="text-5xl lg:text-6xl font-bold text-white leading-relaxed mb-6 overflow-visible  opacity-0 animate-fadeIn">
            <span className="block opacity-0 animate-slideInFromLeft" style={{ animationDelay: '0.3s' }}>
              We Don't Just Build Websites —
            </span>
            <span className="block bg-gradient-to-r from-pink-500 to-purple-500 text-transparent bg-clip-text opacity-0 pb-1.5 animate-slideInFromRight" style={{ animationDelay: '0.8s' }}>
              We create intelligent digital experiences
            </span>
          </h1>
          <p className="text-xl text-purple-100/80 mb-8 max-w-4xl mx-auto opacity-0 animate-fadeIn" style={{ animationDelay: '1.2s' }}>
            From sleek one-pagers to more advanced platforms, we deliver websites that are fast, responsive, and fully functional.
          </p>
          <div className="opacity-0 animate-fadeIn" style={{ animationDelay: '1.5s' }}>
            <Link href="/contact?service=webdev#contact-form">
              <Button
                size="lg"
                className="bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-500 hover:to-purple-500 text-white shadow-lg shadow-purple-500/20 transition-all duration-200 hover:scale-105"
              >
                Get Started
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>

      {/* Benefits Section */}
      <div className="py-20 px-4 sm:px-6 lg:px-8 bg-purple-950/50">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-3xl font-bold text-white text-center mb-12">
            Our <span className="bg-gradient-to-r from-pink-500 to-purple-500 text-transparent bg-clip-text">AI-Enhanced</span> Workflow
<br/>
Human-led, tool-assisted — for faster, smarter, better results.
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: Zap,
                title: "Lightning Fast",
                description: "Product can be delivered in days, not weeks with our streamlined process, but of course depending on the sophistication of the task"
              },
              {
                icon: Cpu,
                title: "Smart Workflows",
                description: "AI-enhanced development reduces time while increasing quality"
              },
              {
                icon: LayoutTemplate,
                title: "Clean Design",
                description: "Modern, responsive layouts that convert visitors"
              },
              {
                icon: Search,
                title: "SEO Optimized",
                description: "Built to rank from day one with smart content suggestions"
              },
              {
                icon: Smartphone,
                title: "Mobile-First",
                description: "Perfectly responsive across all devices"
              },
              {
                icon: Link2,
                title: "Seamless Integrations",
                description: "Chatbots, CRMs and more connect effortlessly"
              }
            ].map((benefit, index) => (
              <div
                key={index}
                className="bg-purple-900/20 backdrop-blur-sm rounded-lg p-6 border border-purple-700/20 hover:bg-purple-900/30 transition-colors duration-200 hover:scale-105 transform"
              >
                <div className="bg-gradient-to-br from-pink-500 to-purple-500 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                  <benefit.icon className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">{benefit.title}</h3>
                <p className="text-purple-100/70">{benefit.description}</p>
              </div>
            ))}
          </div>

          {/* Tech Stack Section */}
          <div className="mt-16">
            <h3 className="text-2xl font-bold text-white text-center mb-8">
              Our <span className="bg-gradient-to-r from-pink-500 to-purple-500 text-transparent bg-clip-text">Tech Stack</span>
            </h3>
            <p className="text-center text-purple-100/70 mb-10 max-w-3xl mx-auto">
              We leverage modern technologies to build robust, scalable, and high-performance web solutions.
            </p>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-8 max-w-5xl mx-auto">
              {[
                { name: "JavaScript", logo: "/logo/tech-stack/javascript.svg" },
                { name: "Node.js", logo: "/logo/tech-stack/nodejs.svg" },
                { name: "TypeScript", logo: "/logo/tech-stack/typescript.svg" },
                { name: "Vue.js", logo: "/logo/tech-stack/vuejs.svg" },
                { name: "Python", logo: "/logo/tech-stack/python.svg" },
                { name: "Astro", logo: "/logo/tech-stack/astro.svg" },
                { name: "React / React Native", logo: "/logo/tech-stack/react.svg" },
                { name: "Next.js", logo: "/logo/tech-stack/nextjs.svg" },
                { name: "Angular", logo: "/logo/tech-stack/angular.svg" },
                { name: "Supabase", logo: "/logo/tech-stack/supabase.svg" },
                { name: "Svelte", logo: "/logo/tech-stack/svelte.svg" },
                { name: "Gatsby", logo: "/logo/tech-stack/gatsby.svg" }
              ].map((tech, index) => (
                <div
                  key={index}
                  className="flex flex-col items-center justify-center p-4 bg-purple-900/20 backdrop-blur-sm rounded-lg border border-purple-700/20 hover:bg-purple-900/30 transition-all duration-200 hover:scale-105 transform"
                >
                  <div className="w-12 h-12 mb-3 flex items-center justify-center">
                    <img
                      src={tech.logo}
                      alt={`${tech.name} logo`}
                      className="max-w-full max-h-full"
                    />
                  </div>
                  <span className="text-purple-100 text-sm font-medium text-center">{tech.name}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Packages Section - Reverting HeroBackground to wrap the whole section */}
      <div className="py-20 px-4 sm:px-6 lg:px-8 bg-white"> {/* Removed HeroBackground, plain div */}
        <div className="relative max-w-7xl mx-auto">
          <h2 className="text-3xl font-bold text-purple-950 text-center mb-6">
            Our Web Development{" "}
            <span className="bg-gradient-to-r from-pink-500 to-purple-500 text-transparent bg-clip-text">
              Packages
            </span>
          </h2>
          <p className="text-center text-purple-600/80 mb-10 max-w-3xl mx-auto text-sm">
            These packages are approximate classifications. In your free consultation, we'll help determine the exact level of sophistication your project requires.
          </p>
          {/* Grid itself doesn't need HeroBackground now */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Package 1: Essentials */}
            {/* Removed z-10 from cards as parent div is now relative */}
            <Card className="bg-white border-2 border-purple-200 hover:border-purple-500 text-purple-950 flex flex-col hover:shadow-xl transition-all duration-300 h-full hover:scale-105">
              <CardHeader>
                <CardTitle className="text-2xl font-semibold bg-gradient-to-r from-pink-500 to-purple-500 text-transparent bg-clip-text">
                  Website Essentials
                </CardTitle>
                <CardDescription className="text-purple-600">
                  For starters or small businesses
                </CardDescription>
              </CardHeader>
              <CardContent className="flex-grow flex flex-col">
                <ul className="space-y-3 text-purple-800 mb-6 flex-grow">
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>Modern Designs — Sleek, mobile-friendly, and built to convert</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>Fast Launch — Delivered in days, not weeks</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>Basic Contact Forms — Let customers reach you with ease</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>SEO-Ready — Get found on Google from day one</span>
                  </li>
                </ul>
                <Link href="/contact?service=webdev-essential" className="mt-auto">
                  <Button className="w-full bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-500 hover:to-purple-500 text-white shadow-md hover:shadow-lg transition-all duration-200 hover:scale-[1.02]">
                    Select This Package
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Package 2: Smart Business */}
            {/* Removed z-10 from cards */}
            <Card className="bg-white border-2 border-purple-200 hover:border-purple-500 text-purple-950 shadow-xl shadow-purple-500/20 flex flex-col hover:shadow-2xl transition-all duration-300 h-full hover:scale-105">
              <CardHeader>
                <CardTitle className="text-2xl font-semibold bg-gradient-to-r from-pink-500 to-purple-500 text-transparent bg-clip-text">
                  Smart Business Websites
                </CardTitle>
                <CardDescription className="text-purple-600">
                  For growing businesses
                </CardDescription>
              </CardHeader>
              <CardContent className="flex-grow flex flex-col">
                <ul className="space-y-3 text-purple-800 mb-6 flex-grow">
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>Custom Features — Booking systems, menus, galleries, testimonials, etc.</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>Analytics Integration — Track user behavior and performance</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>Lead Capture & Forms — Integrated with your CRM or email tools</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>Chatbot Integration — Engage visitors instantly</span>
                  </li>
                   <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>Multi-language Options (Optional)</span>
                  </li>
                </ul>
                <Link href="/contact?service=webdev-business" className="mt-auto">
                  <Button className="w-full bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-500 hover:to-purple-500 text-white shadow-md hover:shadow-lg transition-all duration-200 hover:scale-[1.02]">
                    Select This Package
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Package 3: Advanced */}
            {/* Removed z-10 from cards */}
            <Card className="bg-white border-2 border-purple-200 hover:border-purple-500 text-purple-950 flex flex-col hover:shadow-xl transition-all duration-300 h-full hover:scale-105">
              <CardHeader>
                <CardTitle className="text-2xl font-semibold bg-gradient-to-r from-pink-500 to-purple-500 text-transparent bg-clip-text">
                  Advanced Web Platforms
                </CardTitle>
                <CardDescription className="text-purple-600">
                  For more complex needs
                </CardDescription>
              </CardHeader>
              <CardContent className="flex-grow flex flex-col">
                <ul className="space-y-3 text-purple-800 mb-6 flex-grow">
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>Full Frontend + Backend Development — Tailored portals, dashboards, internal tools</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>Admin Interfaces — Control your content, users, and data</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>User Authentication — Secure login, registration, permissions, etc.</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>API Integrations — Payments, CRMs, third-party tools, etc.</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-pink-500 mr-2 mt-1 flex-shrink-0" />
                    <span>Automation Workflows — Reduce manual work with smart backend logic</span>
                  </li>
                </ul>
                <Link href="/contact?service=webdev-advanced" className="mt-auto">
                  <Button className="w-full bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-500 hover:to-purple-500 text-white shadow-md hover:shadow-lg transition-all duration-200 hover:scale-[1.02]">
                    Select This Package
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </div> {/* Close plain div wrapper for the section */}

      {/* Enhanced All Projects Include Section */}
      <div className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-purple-800 to-purple-900 relative overflow-hidden">
        {/* Background decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full opacity-5 pointer-events-none">
          <div className="absolute top-10 left-10 w-64 h-64 rounded-full bg-gradient-to-r from-pink-500 to-purple-500 blur-3xl"></div>
          <div className="absolute bottom-10 right-10 w-64 h-64 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 blur-3xl"></div>
        </div>

        <div className="max-w-5xl mx-auto text-center relative z-10">
           <h2 className="text-4xl font-bold text-white mb-12">
            Every Project <span className="bg-gradient-to-r from-pink-500 to-purple-500 text-transparent bg-clip-text">Includes These Essentials</span>
          </h2>

          <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-x-12 md:gap-y-8 text-left max-w-6xl mx-auto px-4">
             {[
              "Fully Responsive Design",
              "Speed-Optimized Performance",
              "Solid SEO Foundations",
              "Essential Security Setup",
              "Transparent & Collaborative Process",
              "Scalable foundations for future growth"
            ].map((feature, index) => (
              <div
                key={index}
                className="group flex items-center p-4 rounded-xl border border-purple-700/30 backdrop-blur-sm bg-purple-900/20 hover:bg-purple-900/40 hover:border-pink-500/50 transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-purple-500/10"
              >
                <div className="flex-shrink-0 mr-4 relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-pink-500 to-purple-500 rounded-full opacity-0 group-hover:opacity-100 blur-md transition-opacity duration-300"></div>
                  <div className="relative bg-gradient-to-br from-pink-500 to-purple-500 w-10 h-10 rounded-full flex items-center justify-center transform group-hover:scale-110 transition-transform duration-300 shadow-md shadow-purple-500/20">
                    <CheckCircle className="w-5 h-5 text-white" />
                  </div>
                </div>
                <span className="text-lg text-purple-100/90 group-hover:text-white transition-colors duration-300">{feature}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* New CTA Section */}
      <div className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-pink-500 to-purple-500"> {/* Pink gradient background */}
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl font-bold text-white mb-6">
            Want to elevate your business?
          </h2>
          <p className="text-xl text-purple-100/80 mb-8">
            Let's discuss how a tailored web solution can drive growth.
          </p>
          <Link href="/contact?service=webdev">
            <Button
              size="lg"
              className="bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-500 hover:to-purple-500 text-white shadow-lg shadow-purple-500/20 transition-all duration-200 hover:scale-105"
            >
              Book a free strategy call!
            </Button>
          </Link>
        </div>
      </div>

    </div>
  );
}
