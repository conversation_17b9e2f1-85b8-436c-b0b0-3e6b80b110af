# Complete Hosting Guide for Next.js with API Routes

## Understanding Next.js Hosting Requirements

### What Your App Needs:
1. **Static Assets**: HTML, CSS, JS, images (served by CDN/web server)
2. **API Routes**: Server-side endpoints that need Node.js runtime
3. **Environment Variables**: Secure storage for API keys
4. **Build Process**: Compilation and optimization

## Hosting Provider Categories

### 🟢 **Category 1: Full Next.js Platforms (Recommended)**
**Best for**: Complete Next.js support, zero configuration

#### **Vercel** (Made by Next.js creators)
```bash
# Deployment
npm install -g vercel
vercel

# Or connect GitHub repo in Vercel dashboard
```
**Features**:
- ✅ Automatic Next.js optimization
- ✅ Edge functions for API routes
- ✅ Automatic HTTPS
- ✅ Global CDN
- ✅ Environment variables in dashboard
- ✅ Git integration

**Cost**: Free tier available, pay for usage

---

#### **Netlify** (With Next.js Runtime)
```bash
# Build settings in Netlify dashboard:
Build command: npm run build
Publish directory: .next
```
**Features**:
- ✅ Next.js runtime support
- ✅ Serverless functions
- ✅ Form handling
- ✅ CDN included

---

### 🟡 **Category 2: Node.js Hosting Providers**
**Best for**: Traditional hosting, more control, cost-effective

#### **Hostinger** (VPS/Cloud Hosting)
**Requirements**: Node.js hosting plan

**Setup Process**:
```bash
# 1. Upload project files via FTP/Git
# 2. SSH into server
ssh username@your-server-ip

# 3. Navigate to project directory
cd /path/to/your/project

# 4. Install Node.js (if not installed)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 5. Install dependencies
npm install

# 6. Build the application
npm run build

# 7. Start the application
npm start
```

**Environment Variables**:
```bash
# Create .env.production file
nano .env.production

# Add your variables:
MAILGUN_API_KEY=your_key_here
MAILGUN_DOMAIN=mg.upzera.com
MAILGUN_FROM=UpZera <<EMAIL>>
MAILGUN_REGION=eu
```

**Process Management** (Keep app running):
```bash
# Install PM2 (Process Manager)
npm install -g pm2

# Start your app with PM2
pm2 start npm --name "upzera" -- start

# Save PM2 configuration
pm2 save
pm2 startup
```

**Nginx Configuration** (Reverse Proxy):
```nginx
# /etc/nginx/sites-available/upzera.com
server {
    listen 80;
    server_name upzera.com www.upzera.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

---

#### **DigitalOcean Droplet**
**Setup**: Similar to Hostinger VPS

```bash
# 1. Create Ubuntu droplet
# 2. Install Node.js, Nginx, PM2
# 3. Clone your repository
git clone https://github.com/yourusername/upzera.git
cd upzera

# 4. Install and build
npm install
npm run build

# 5. Start with PM2
pm2 start npm --name "upzera" -- start
```

---

#### **Railway**
```bash
# Connect GitHub repo in Railway dashboard
# Railway automatically detects Next.js and deploys
```

---

### 🔴 **Category 3: Static-Only Hosting**
**Limitation**: Cannot run API routes

#### **GitHub Pages, Surge.sh, Firebase Hosting**
**Workaround**: Use external services for forms
- Formspree
- EmailJS
- External API endpoints

## Step-by-Step: Hostinger VPS Deployment

### Prerequisites:
- Hostinger VPS plan with Node.js support
- Domain pointed to your server
- SSH access

### 1. **Server Setup**
```bash
# Connect via SSH
ssh root@your-server-ip

# Update system
apt update && apt upgrade -y

# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
apt-get install -y nodejs

# Install Nginx
apt install nginx -y

# Install PM2
npm install -g pm2
```

### 2. **Deploy Your App**
```bash
# Clone your repository
git clone https://github.com/yourusername/upzera.git
cd upzera

# Install dependencies
npm install

# Create production environment file
nano .env.production
# Add your Mailgun credentials

# Build the application
npm run build

# Test the application
npm start
# Should run on http://localhost:3000
```

### 3. **Configure Process Manager**
```bash
# Start with PM2
pm2 start npm --name "upzera" -- start

# Save PM2 configuration
pm2 save

# Set PM2 to start on boot
pm2 startup
# Follow the instructions it provides
```

### 4. **Configure Nginx**
```bash
# Create Nginx configuration
nano /etc/nginx/sites-available/upzera.com

# Add the configuration (see above)

# Enable the site
ln -s /etc/nginx/sites-available/upzera.com /etc/nginx/sites-enabled/

# Test Nginx configuration
nginx -t

# Restart Nginx
systemctl restart nginx
```

### 5. **SSL Certificate (Optional but Recommended)**
```bash
# Install Certbot
apt install certbot python3-certbot-nginx -y

# Get SSL certificate
certbot --nginx -d upzera.com -d www.upzera.com
```

## Environment Variables Setup

### For VPS/Traditional Hosting:
```bash
# .env.production file
MAILGUN_API_KEY=**************************************************
MAILGUN_DOMAIN=mg.upzera.com
MAILGUN_FROM=UpZera <<EMAIL>>
MAILGUN_REGION=eu
NODE_ENV=production
```

### For Platform Hosting (Vercel/Netlify):
- Add via dashboard interface
- Same variable names and values

## Monitoring and Maintenance

### Check Application Status:
```bash
# PM2 status
pm2 status

# View logs
pm2 logs upzera

# Restart application
pm2 restart upzera

# Check Nginx status
systemctl status nginx
```

### Updates:
```bash
# Pull latest changes
git pull origin main

# Rebuild
npm run build

# Restart
pm2 restart upzera
```

## Troubleshooting Common Issues

### 1. **API Routes Not Working**
- ✅ Ensure `output: 'export'` is removed from `next.config.js`
- ✅ Check environment variables are set
- ✅ Verify Node.js server is running

### 2. **Static Assets Not Loading**
- ✅ Check Nginx configuration
- ✅ Verify build completed successfully
- ✅ Check file permissions

### 3. **Email Not Sending**
- ✅ Verify Mailgun API key and domain
- ✅ Check server logs: `pm2 logs upzera`
- ✅ Test Mailgun credentials separately

## Recommendation

**For Beginners**: Use **Vercel** or **Netlify** (automatic deployment)
**For Control/Cost**: Use **Hostinger VPS** or **DigitalOcean** (manual setup)
**For Enterprise**: Use **AWS**, **Google Cloud**, or **Azure**

Your current setup will work perfectly on any of these platforms!
