import { cn } from "@/lib/utils"; // Corrected import path
import { ElementType, ComponentPropsWithoutRef } from "react";

interface StarBorderProps<T extends ElementType> {
  as?: T;
  color?: string;
  speed?: string;
  className?: string;
  children: React.ReactNode;
}

export function StarBorder<T extends ElementType = "button">({
  as,
  className,
  color,
  speed = "6s",
  children,
  ...props
}: StarBorderProps<T> & Omit<ComponentPropsWithoutRef<T>, keyof StarBorderProps<T>>) {
  const Component = as || "button";
  const defaultColor = color || "hsl(var(--foreground))";

  return (
    <Component
      className={cn(
        "relative inline-block overflow-hidden rounded-[20px] p-[1px]", // Added p-[1px] for better corner protection
        className
      )}
      {...props}
    >
      {/* Added a protective layer for corners */}
      <div className="absolute inset-0 rounded-[20px] bg-transparent z-0"></div>

      {/* Animation elements - adjusted positioning to prevent overflow */}
      <div
        className={cn(
          "absolute w-[300%] h-[50%] bottom-[-8px] right-[-240%] rounded-full animate-star-movement-bottom z-0",
          "opacity-20 dark:opacity-70"
        )}
        style={{
          background: `radial-gradient(circle, ${defaultColor}, transparent 10%)`,
          animationDuration: speed,
          transformOrigin: "center center", // Added for better animation control
        }}
      />
      <div
        className={cn(
          "absolute w-[300%] h-[50%] top-[-8px] left-[-240%] rounded-full animate-star-movement-top z-0",
          "opacity-20 dark:opacity-70"
        )}
        style={{
          background: `radial-gradient(circle, ${defaultColor}, transparent 10%)`,
          animationDuration: speed,
          transformOrigin: "center center", // Added for better animation control
        }}
      />

      {/* Content container with reinforced corners */}
      <div className={cn(
        "relative z-1 text-foreground text-center text-base py-4 px-6 rounded-[18px] overflow-hidden"
        // Slightly smaller radius to ensure it stays within the parent
      )}>
        {children}
      </div>
    </Component>
  );
}
