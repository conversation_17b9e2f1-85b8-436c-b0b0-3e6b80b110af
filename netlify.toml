[build]
  publish = "out"
  command = "npm run build"

[functions]
  directory = "netlify/functions"

# Redirect API routes to Netlify Functions
[[redirects]]
  from = "/api/contact"
  to = "/.netlify/functions/contact"
  status = 200

[[redirects]]
  from = "/api/newsletter"
  to = "/.netlify/functions/newsletter"
  status = 200

# Handle client-side routing for Next.js
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  conditions = {Role = ["admin"]}

# Environment variables (these will be set in Netlify dashboard)
# MAILGUN_API_KEY
# MAILGUN_DOMAIN
# MAILGUN_FROM
# MAILGUN_REGION
