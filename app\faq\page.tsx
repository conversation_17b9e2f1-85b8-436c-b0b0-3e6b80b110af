import React from 'react';
import { FaqSection } from "@/components/ui/faq-section";
import Link from 'next/link';
import { Button } from "@/components/ui/button";
import { Hourglass } from "lucide-react";

const UPZERA_FAQS = [
  {
    question: "What kind of businesses do you work with?",
    answer: "We typically work with startups, small teams, and growing businesses that need fast, reliable digital tools — from landing pages to more complex designs. Whether you're just starting or scaling, we're here to help."
  },
  {
    question: "How much do your services cost?",
    answer: "You'll receive a clear, personalized quote after a free consultation.<br />Our pricing is project-based and depends on your specific goals and scope. Most projects range from €350–€500 for landing pages, €1,400-€2,400 for full websites, and €500-€1,200 for chatbot setups."
  },
  {
    question: "How fast can you deliver a project?",
    answer: "Timelines vary based on complexity, but most landing pages are ready within a few days, while full builds typically take 2–4 weeks. We’ll give you a clear timeline before we start — and we stick to it."
  },
  {
    question: "Do you offer ongoing support?",
    answer: "Absolutely — but only when you need it. We’re here post-launch for fixes, updates, or improvements. You won't be handed off to a helpdesk — just reach out and we’ll help."
  },
  {
    question: "What makes you different from other web development teams?",
    answer: "We’re engineers at heart, obsessed with clean solutions. We use agentic AI workflows to deliver faster without sacrificing quality — and we tailor everything to your business, not some cookie-cutter template."
  },
  {
    question: "How do I get started?",
    answer: "Simple — just book a free consultation. We’ll talk about your goals, assess your needs, and give you a custom game plan."
  },
  {
    question: "How do you handle invoicing and payment?",
    answer: `<div class="space-y-4">
      <p>We keep payments simple and transparent:</p>
      <ul class="list-disc list-inside space-y-2 pl-4">
        <li><b>Free concept preview</b> - We'll create a small design preview to ensure we're the right fit</li>
        <li><b>Contract signing</b> - If you like the direction, we'll formalize the agreement</li>
        <li><b>Deposit payment</b> - 20% for small projects or milestone-based for larger ones</li>
        <li><b>Clear invoicing</b> - Based on agreed milestones or final delivery</li>
        <li><b>Flexible terms</b> - Standard NET15 payment terms, but we can accommodate your needs</li>
      </ul>
      <p>No surprises — just straightforward payment terms that work for both of us.</p>
    </div>`
  },
  {
    question: "What do you need to start a project?",
    answer: `<div class="space-y-4">
      <p>Our streamlined 5-step process:</p>
      <ol class="list-decimal list-inside space-y-3 pl-4">
        <li class="font-medium">Intro Call<br />
          <span class="font-normal text-purple-100/80">We'll discuss your vision and requirements</span>
        </li>
        <li class="font-medium">Optional Preview<br />
          <span class="font-normal text-purple-100/80">Free concept demo to ensure alignment</span>
        </li>
        <li class="font-medium">Agreement & Deposit<br />
          <span class="font-normal text-purple-100/80">Sign contract and pay deposit</span>
        </li>
        <li class="font-medium">Materials Handoff<br />
          <span class="font-normal text-purple-100/80">Share your assets and access details</span>
        </li>
        <li class="font-medium">Build Phase<br />
          <span class="font-normal text-purple-100/80">We develop your solution efficiently</span>
        </li>
      </ol>
      <p>Simple, transparent, and focused on delivering exactly what you need.</p>
    </div>`
  },
];

export default function FAQPage() {
  return (
    <div className="min-h-screen pt-28 pb-20 bg-gradient-to-b from-purple-950 via-purple-900/95 to-purple-950 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
        <FaqSection
          title="Frequently Asked Questions"
          description="Find answers to common questions about our services and approach"
          items={UPZERA_FAQS}
          className="text-white w-full"
        />
      </div>

      {/* Bottom CTA Section with purple-pink gradient */}
      <section className="w-full py-20 mt-16 bg-gradient-to-r from-purple-700 via-purple-600 to-pink-500">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-8">
            Still have questions?
          </h2>
          <p className="text-xl text-white/90 max-w-3xl mx-auto mb-10">
            We're here to help with any questions you might have about our services
          </p>

          <Link href="/contact">
            <Button
              size="lg"
              className="w-full sm:w-auto bg-white/15 hover:bg-white/25 text-white border border-white/20
                         shadow-lg transition-all duration-300 hover:scale-105 hover:shadow-xl text-lg px-4 py-3 sm:px-8 sm:py-6"
            >
              Contact Us
            </Button>
          </Link>
        </div>
      </section>
    </div>
  );
}