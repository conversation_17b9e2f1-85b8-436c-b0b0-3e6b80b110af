// types/splinetool.d.ts
declare module '@splinetool/react-spline' {
  import React from 'react';

  interface SplineProps extends React.HTMLAttributes<HTMLDivElement> {
    scene: string;
    onLoad?: (spline: any) => void; // Use 'any' for the spline object type if specific type is unknown
    // Add other props based on the library's documentation if needed
  }

  const Spline: React.FC<SplineProps>;
  export default Spline;
}
