# Netlify Deployment Guide for UpZera

## Problem Fixed
The contact and newsletter forms were failing on the hosted Netlify server because:
1. Next.js was configured with `output: 'export'` (static export)
2. Static exports don't support API routes (`/api/contact` and `/api/newsletter`)
3. The forms worked locally because `next dev` includes a development server

## Solution Implemented
Converted API routes to **Netlify Functions** which can handle server-side logic on Netlify's platform.

## Files Created/Modified

### New Files:
- `netlify/functions/contact.js` - Handles contact form submissions
- `netlify/functions/newsletter.js` - Handles newsletter subscriptions
- `netlify/functions/package.json` - Dependencies for Netlify Functions
- `netlify.toml` - Netlify configuration file

### Modified Files:
- `app/contact/page.tsx` - Better error handling
- `components/NewsletterSubscribe.tsx` - Better error handling
- `next.config.js` - Added trailingSlash and distDir config

## Deployment Steps

### 1. Environment Variables
In your Netlify dashboard, go to **Site settings > Environment variables** and add:

```
MAILGUN_API_KEY=**************************************************
MAILGUN_DOMAIN=mg.upzera.com
MAILGUN_FROM=UpZera <<EMAIL>>
MAILGUN_REGION=eu
```

### 2. Build Settings
In Netlify dashboard, set:
- **Build command**: `npm run build`
- **Publish directory**: `out`

### 3. Deploy
Push your changes to your repository. Netlify will automatically:
1. Install dependencies
2. Build the static site
3. Deploy Netlify Functions
4. Set up redirects from `/api/*` to `/.netlify/functions/*`

## How It Works

### API Route Redirects
The `netlify.toml` file redirects:
- `/api/contact` → `/.netlify/functions/contact`
- `/api/newsletter` → `/.netlify/functions/newsletter`

### Netlify Functions
- Run in a serverless environment
- Have access to environment variables
- Can use Node.js modules (Mailgun, form-data)
- Handle CORS automatically

### Email Flow
1. **Contact Form**: Sends notification to admin team + confirmation to user
2. **Newsletter**: Sends notification to admin team + welcome email to subscriber

## Testing
After deployment:
1. Test contact form submission
2. Test newsletter subscription
3. Check email delivery to both admin and user emails

## Troubleshooting

### If forms still fail:
1. Check Netlify Function logs in dashboard
2. Verify environment variables are set correctly
3. Ensure Mailgun domain is verified
4. Check CORS headers in browser developer tools

### Common Issues:
- **500 errors**: Usually environment variable issues
- **CORS errors**: Functions should handle CORS automatically
- **Email not sending**: Check Mailgun API key and domain settings

## Benefits of This Solution
- ✅ Works with static export (faster loading)
- ✅ Serverless functions (cost-effective)
- ✅ Same email functionality as before
- ✅ Better error handling
- ✅ CORS handled automatically
- ✅ No changes needed to form UI/UX
