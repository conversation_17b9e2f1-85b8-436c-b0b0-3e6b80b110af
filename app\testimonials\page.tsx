"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { GradientBorder } from "@/components/ui/gradient-border";
import Link from "next/link";
import { CheckCircle2, Calendar, Layout, Palette, <PERSON>, Clock, Smartphone, Zap } from "lucide-react";
import { motion } from "framer-motion";

export default function TestimonialsPage() {
  return (
    <div className="min-h-screen overflow-x-hidden bg-gradient-to-br from-purple-950 via-purple-900 to-purple-950 text-white">
      {/* Hero Section - Enhanced */}
      <div className="pt-32 pb-16 px-4 sm:px-6 lg:px-8 text-center relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-b from-purple-950/80 to-transparent z-0"></div>
        {/* Decorative gradient blobs */}
        <div className="absolute top-1/4 right-0 w-72 h-72 bg-pink-500/20 rounded-full filter blur-3xl opacity-50 animate-blob"></div>
        <div className="absolute bottom-0 left-1/4 w-72 h-72 bg-purple-500/20 rounded-full filter blur-3xl opacity-50 animate-blob animation-delay-2000"></div>
        <div className="absolute top-1/3 left-0 w-72 h-72 bg-blue-500/20 rounded-full filter blur-3xl opacity-50 animate-blob animation-delay-4000"></div>
        
        <div className="relative z-10">
          <h1 className="text-4xl lg:text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-pink-300 to-white">
            Client Success Stories
          </h1>
          <p className="text-xl text-white/90 max-w-3xl mx-auto">
            See how we've helped businesses transform their digital presence and achieve their goals.
          </p>
        </div>
      </div>

      {/* Add animation styles */}
      <style jsx global>{`
        @keyframes blob {
          0% {
            transform: scale(1) translate(0px, 0px);
          }
          33% {
            transform: scale(1.1) translate(30px, -50px);
          }
          66% {
            transform: scale(0.9) translate(-20px, 20px);
          }
          100% {
            transform: scale(1) translate(0px, 0px);
          }
        }
        .animate-blob {
          animation: blob 7s infinite;
        }
        .animation-delay-2000 {
          animation-delay: 2s;
        }
        .animation-delay-4000 {
          animation-delay: 4s;
        }
      `}</style>

      {/* Featured Project Section */}
      <div className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          {/* Project showcase card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, margin: "-100px" }}
            transition={{ duration: 0.8, ease: "easeOut" }}
          >
            <Card className="w-full bg-purple-900/30 backdrop-blur-sm border border-purple-700/30 overflow-hidden rounded-2xl shadow-xl transition-all duration-300 hover:shadow-2xl hover:border-purple-500/50">
            {/* Top ribbon */}
            <div className="bg-gradient-to-r from-pink-600 to-purple-600 py-3 px-6">
              <p className="text-sm font-medium text-white/90 flex items-center">
                <span className="mr-2">✨</span> Featured Project: Qochi Services
              </p>
            </div>
            
            <div className="md:flex">
              {/* Left column - Mockup */}
              <div className="md:w-1/2 p-6 md:p-8 flex flex-col items-center justify-center gap-4">
                {/* Browser Mockup */}
                <div className="w-full max-w-xl rounded-xl overflow-hidden shadow-2xl bg-gray-800 border border-gray-600">
                  {/* Browser Top Bar */}
                  <div className="h-8 bg-gray-700 flex items-center px-3">
                    <div className="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
                    <div className="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                    <div className="w-3 h-3 rounded-full bg-green-500"></div>
                  </div>
                  {/* Browser Content - Image */}
                  <img 
                    src="/testimonials/C_tutor_web.png" 
                    alt="Alexandre C Website Screenshot" 
                    className="w-full h-auto block hover:scale-[1.02] transition-transform duration-300" 
                  />
                </div>
                {/* Check it out button */}
                <Link href="https://alexandre-c.netlify.app" target="_blank" rel="noopener noreferrer">
                  <Button 
                    variant="outline" 
                    className="w-full sm:w-auto bg-gradient-to-r from-pink-600 to-purple-600 text-white border-0 hover:from-pink-500 hover:to-purple-500 hover:text-white px-4 py-2 sm:px-6 sm:py-3 text-lg font-medium"
                  >
                    Check it out →
                  </Button>
                </Link>
              </div>
              
              {/* Right column - Content */}
              <div className="md:w-3/5 p-8">
                <h2 className="text-2xl md:text-3xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-white to-pink-100">
                  How We Helped Qochi Launch a Clean, Modern Tutoring Platform
                </h2>
                
                {/* Quote section */}
                <div className="my-6 relative">
                  <blockquote className="italic relative">
                    <div className="absolute -left-1 -top-1 text-4xl text-pink-500 opacity-50">"</div>
                    <div className="absolute -right-1 -bottom-1 text-4xl text-pink-500 opacity-50">"</div>
                    <p className="pl-6 pr-6 italic text-lg text-white border-l-2 border-pink-500 transition-all duration-300 hover:border-pink-400 hover:pl-7">
                        UpZera made the entire process seamless. The site is fast, beautiful, and our students love how easy it is to book sessions.
                    </p>
                    <footer className="mt-2 font-normal text-sm text-purple-200 not-italic">
                      — Founder of Qochi Services
                    </footer>
                  </blockquote>
                </div>
                
                {/* The Challenge */}
                <div className="mb-6">
                  <h3 className="font-semibold text-xl flex items-center mb-2 text-white">
                    <span className="mr-2 text-purple-400">●</span> The Challenge
                  </h3>
                  <p className="text-white/90">
                    Qochi needed a clean, modern website to showcase their tutoring services — with the ability for students to easily book sessions online and engage with the brand.
                  </p>
                </div>
              </div>
            </div>
            
            {/* Bottom section */}
            <div className="p-8 pt-0 space-y-12"> {/* Kept space-y-12 */}
              {/* What We Delivered */}
              <div className="bg-gray-100 rounded-xl p-6 shadow-lg"> {/* Changed background to bg-gray-100 */}
                <h3 className="font-semibold text-xl flex items-center mb-6 text-purple-900 border-b border-purple-300 pb-2"> {/* Adjusted heading color and border */}
                  <span className="mr-2 text-purple-600">🛠️</span> What We Delivered
                </h3>
                <div className="grid md:grid-cols-2 gap-6">
                  {/* Item 1 */}
                  <div className="flex items-start">
                    <div className="bg-gradient-to-br from-pink-500 to-purple-500 w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0 mr-4 transition-transform duration-200 hover:scale-110">
                      <Zap className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="font-medium bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Responsive, fast-loading website</p> {/* Applied gradient text */}
                      <p className="text-sm text-gray-600">With a sleek, professional layout</p> {/* Adjusted secondary text color */}
                    </div>
                  </div>
                  {/* Item 2 */}
                  <div className="flex items-start">
                    <div className="bg-gradient-to-br from-pink-500 to-purple-500 w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0 mr-4 transition-transform duration-200 hover:scale-110">
                      <Calendar className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="font-medium bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Booking integration using Calendly</p> {/* Applied gradient text */}
                      <p className="text-sm text-gray-600">Allowing clients to reserve tutoring sessions effortlessly</p> {/* Adjusted secondary text color */}
                    </div>
                  </div>
                  {/* Item 3 */}
                  <div className="flex items-start">
                    <div className="bg-gradient-to-br from-pink-500 to-purple-500 w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0 mr-4 transition-transform duration-200 hover:scale-110">
                      <Brain className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="font-medium bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Thoughtful content layout</p> {/* Applied gradient text */}
                      <p className="text-sm text-gray-600">Focused on trust, clarity, and conversion</p> {/* Adjusted secondary text color */}
                    </div>
                  </div>
                  {/* Item 4 */}
                  <div className="flex items-start">
                    <div className="bg-gradient-to-br from-pink-500 to-purple-500 w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0 mr-4 transition-transform duration-200 hover:scale-110">
                      <Palette className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="font-medium bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Simple, elegant design system</p> {/* Applied gradient text */}
                      <p className="text-sm text-gray-600">That reflects the educational tone of Qochi's brand</p> {/* Adjusted secondary text color */}
                    </div>
                  </div>
                </div>
              </div>

              {/* Tech Stack */}
              <div className="bg-gray-100 rounded-xl p-6 shadow-lg">
                <h3 className="font-semibold text-xl flex items-center mb-6 text-purple-900 border-b border-purple-300 pb-2">
                  <span className="mr-2 text-purple-600">🖥️</span> Tech Stack
                </h3>
                <div className="flex flex-row gap-8 justify-center items-center"> {/* Outer: Row, center horizontally and vertically */}
                  <div className="flex flex-col items-center gap-2"> {/* Inner React: Column, center items horizontally */}
                    <img
                      src="/logo/tech-stack/react.svg"
                      alt="React Logo"
                      className="h-10 w-10"
                    />
                    <span className="font-medium text-lg text-purple-800">React</span>
                  </div>
                  <div className="flex flex-col items-center gap-2"> {/* Inner Vite: Column, center items horizontally */}
                    <img
                      src="/logo/vite-icon.svg"
                      alt="Vite Logo"
                      className="h-10 w-10"
                    />
                    <span className="font-medium text-lg text-purple-800">Vite</span>
                  </div>
                </div>
           
              </div>
              
              {/* Results */}
              <div className="bg-gray-100 rounded-xl p-6 shadow-lg"> {/* Changed background to bg-gray-100 */}
                <h3 className="font-semibold text-xl flex items-center mb-6 text-purple-900 border-b border-purple-300 pb-2"> {/* Adjusted heading color and border */}
                  <span className="mr-2 text-purple-600">💡</span> Results
                </h3>
                <div className="grid md:grid-cols-2 gap-6">
                  {/* Item 1 */}
                  <div className="p-5">
                    <div className="flex items-center mb-2">
                      <CheckCircle2 className="w-5 h-5 text-green-500 mr-2" /> {/* Adjusted checkmark color slightly */}
                      <p className="font-medium bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Live in just a few days</p> {/* Applied gradient text */}
                    </div>
                    <div className="ml-7">
                      <p className="text-sm text-gray-600"></p> {/* Adjusted secondary text color */}
                    </div>
                  </div>
                  {/* Item 2 */}
                  <div className="p-5">
                    <div className="flex items-center mb-2">
                      <CheckCircle2 className="w-5 h-5 text-green-500 mr-2" /> {/* Adjusted checkmark color slightly */}
                      <p className="font-medium bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Fully mobile-friendly</p> {/* Applied gradient text */}
                    </div>
                    <div className="ml-7">
                      <p className="text-sm text-gray-600">Perfect scores on mobile usability tests</p> {/* Adjusted secondary text color */}
                    </div>
                  </div>
                  {/* Item 3 */}
                  <div className="p-5">
                    <div className="flex items-center mb-2">
                      <CheckCircle2 className="w-5 h-5 text-green-500 mr-2" /> {/* Adjusted checkmark color slightly */}
                      <p className="font-medium bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Smooth booking experience</p> {/* Applied gradient text */}
                    </div>
                    <div className="ml-7">
                      <p className="text-sm text-gray-600"></p> {/* Adjusted secondary text color */}
                    </div>
                  </div>
                  {/* Item 4 */}
                  <div className="p-5">
                    <div className="flex items-center mb-2">
                      <CheckCircle2 className="w-5 h-5 text-green-500 mr-2" /> {/* Adjusted checkmark color slightly */}
                      <p className="font-medium bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Owner can manage everything with ease</p> {/* Applied gradient text */}
                    </div>
                    <div className="ml-7">
                      <p className="text-sm text-gray-600"></p> {/* Adjusted secondary text color */}
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Client Quote */}
              <div className="bg-gray-100 rounded-xl p-6 shadow-lg"> {/* Changed background to bg-gray-100 */}
                <h3 className="font-semibold text-xl flex items-center mb-6 text-purple-900 border-b border-purple-300 pb-2"> {/* Adjusted heading color and border */}
                  <span className="mr-2 text-purple-600">💬</span> From the Client
                </h3>
                <div className="rounded-lg p-6">
                  <blockquote className="italic relative">
                    <div className="absolute -left-1 -top-1 text-4xl text-purple-300">"</div> {/* Adjusted quote mark color */}
                    <div className="absolute -right-1 -bottom-1 text-4xl text-purple-300">"</div> {/* Adjusted quote mark color */}
                    <p className="text-lg z-10 relative px-6 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent leading-relaxed"> {/* Applied gradient text */}
                      The UpZera team made it feel effortless. They understood exactly what I needed, and delivered even better than I imagined.
                    </p>
                  </blockquote>
                </div>
              </div>
            </div>
          </Card>
          </motion.div>
        </div>
      </div>
      
      {/* Standalone CTA Section */}
      <div className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-r from-pink-600 to-purple-600 backdrop-blur-sm border-y border-purple-700/30 mt-32">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-white to-pink-200">
            Want to be our next success story?
          </h2>
          <p className="mb-8 text-xl text-white/90 leading-relaxed font-light max-w-2xl mx-auto">
          Let’s turn your idea into a live product — fast, custom, and fully done-for-you. 
          </p>
          <Link href="/contact">
            <Button 
              size="lg"
              className="w-full sm:w-auto bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-500 hover:to-purple-500 text-white shadow-lg shadow-purple-500/20 transition-all duration-300 hover:scale-105 hover:shadow-xl text-lg px-4 py-3 sm:px-8 sm:py-6"
            >
              Book your free consultation now!
            </Button>
          </Link>

        </div>
      </div>

      {/* Gradient border before footer */}
      <div className="w-full mt-8">
        <GradientBorder 
          width={1920}
          animationDuration={3} // Smoother, slower animation
          strokeWidth={4}
        />
      </div>
      
      {/* Footer is added via layout */}
    </div>
  );
}
