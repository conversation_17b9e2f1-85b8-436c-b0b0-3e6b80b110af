'use client'

import { Suspense, lazy } from 'react'
import { useInView } from 'react-intersection-observer' // Import useInView

const Spline = lazy(() => import('@splinetool/react-spline'))

interface SplineSceneProps {
  scene: string
  className?: string
}

export function SplineScene({ scene, className }: SplineSceneProps) {
  const { ref, inView } = useInView({
    triggerOnce: true, // Only trigger once when it enters the viewport
    threshold: 0.1, // Trigger when 10% of the element is visible
  })

  // Define the loader component separately for clarity
  const Loader = () => (
    <div className="w-full h-full flex items-center justify-center">
      {/* Basic CSS Loader */}
      <style jsx>{`
        .loader {
          width: 48px;
          height: 48px;
          border: 5px solid #FFF; /* White border */
          border-bottom-color: transparent;
          border-radius: 50%;
          display: inline-block;
          box-sizing: border-box;
          animation: rotation 1s linear infinite;
        }

        @keyframes rotation {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
      `}</style>
      <span className="loader"></span>
    </div>
  );

  return (
    // Attach the ref to the container div
    <div ref={ref} className={className} style={{ position: 'relative' }}>
      <style jsx>{`
        /* Reduce Spline canvas resolution by scaling down */
        canvas {
          width: 100% !important;
          height: 100% !important;
          transform: scale(0.75); /* Adjust scale factor as needed */
          transform-origin: top left;
          image-rendering: pixelated;
        }
      `}</style>
      {/* Conditionally render the Spline component only when inView is true */}
      {inView ? (
        <Suspense fallback={<Loader />}>
          <Spline scene={scene} />
        </Suspense>
      ) : (
        // Optionally, render the loader or nothing while not in view
        <Loader />
      )}
    </div>
  )
}
