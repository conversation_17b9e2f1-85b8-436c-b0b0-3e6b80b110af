"use client";

import React from 'react';
import Navbar from '@/components/navbar';

export default function NavbarTest4Page() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-purple-950 via-purple-900/95 to-purple-950 overflow-hidden">
      <Navbar />
      
      <div className="pt-28 pb-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
          <h1 className="text-4xl font-bold text-white mb-8 text-center">Navbar Test - Vertical Service Blocks</h1>
          <p className="text-xl text-white/80 mb-12 text-center">
            This page tests the navbar with vertically stacked rectangular service blocks.
          </p>
          
          <div className="bg-purple-900/30 rounded-xl border border-purple-800/30 p-8 text-white">
            <h2 className="text-2xl font-semibold mb-4">Changes Made:</h2>
            <ul className="list-disc list-inside space-y-2">
              <li>Changed service blocks to be stacked vertically</li>
              <li>Made service blocks more rectangular with longer length</li>
              <li>Changed layout from grid to flex column</li>
              <li>Adjusted internal layout of service blocks to horizontal</li>
              <li>Increased width of the dropdown container</li>
              <li>Adjusted padding and spacing for better appearance</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
