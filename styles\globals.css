/* ...existing code... */

/* Responsive mobile view for process flow */
@media (max-width: 767px) {
  .process-block {
    width: 100%;
    margin-bottom: 10px;
  }

  .pulsing-arrow {
    margin: 5px 0 15px;
  }

  /* No mobile connections */
}

/* Ensure smooth transitions */
.process-flow * {
  transition: all 0.3s ease-in-out;
}

/* No mobile connections for approach page */

/* Animation for wiggly line */
@keyframes dash {
  to {
    stroke-dashoffset: 100;
  }
}

/* Mobile connector styling */
.mobile-connector {
  position: absolute;
  z-index: 0;
}

/* Mobile approach blocks styling */
@media (max-width: 767px) {
  .mobile-connector {
    left: 50% !important;
    transform: translateX(-50%);
  }

  .approach-block {
    border-radius: 16px; /* Smoothed edges but more rectangular */
    padding: 24px 20px;
    min-height: 180px; /* Ensure consistent height for rectangular appearance */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
    max-width: 90%;
    height: auto;
    aspect-ratio: 3 / 2; /* More rectangular shape */
  }

  /* Adjust the flex layout for mobile */
  .approach-block > div:first-child {
    margin-bottom: 16px;
  }
}