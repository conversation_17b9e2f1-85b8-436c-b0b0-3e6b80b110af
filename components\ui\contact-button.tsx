"use client";

import React from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';

interface ContactButtonProps {
  text: string;
  className?: string;
}

export function ContactButton({ text, className }: ContactButtonProps) {
  const router = useRouter();
  
  return (
    <Button 
      size="sm"
      onClick={() => router.push('/contact')}
      className={className}
    >
      {text}
    </Button>
  );
}