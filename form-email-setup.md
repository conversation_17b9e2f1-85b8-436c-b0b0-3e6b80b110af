
# Form Submission Email Setup with <PERSON>oh<PERSON> and Mailgun

## Current Setup
- Business email (`<EMAIL>`) created via **Zoho Mail** (free plan).
- Contact form on the website sends messages using this address.
- Problem: **Zoho free plan does not support automatic email forwarding to aliases or external addresses.**

---

## Recommended Solution: Use Mailgun for Outgoing Emails

### Why Mailgun?
- **Free plan available** (5,000 emails/month for first 3 months).
- Full support for **SMTP and REST API**.
- Reliable and scalable for sending form submissions.

### Benefits
- Bypass Zoho's limitations.
- Easily send form submissions to multiple recipients.
- Maintain `<EMAIL>` as the sender address.

---

## Implementation Options

### 1. **Backend Email Sending with Mailgun (Recommended)**
Use Mailgun SMTP with libraries like:
- `nodemailer` (Node.js)
- `smtplib` (Python)
- `PHPMailer` (PHP)

#### Example (Node.js + Nodemailer):
```js
const nodemailer = require("nodemailer");

let transporter = nodemailer.createTransport({
  host: "smtp.mailgun.org",
  port: 587,
  auth: {
    user: "<EMAIL>",
    pass: "your-mailgun-password"
  }
});

let mailOptions = {
  from: "<EMAIL>",
  to: "<EMAIL>, <EMAIL>",
  subject: "New Contact Form Submission",
  text: "Form content goes here...",
};

transporter.sendMail(mailOptions, function(error, info){
  if (error) console.log(error);
  else console.log("Email sent: " + info.response);
});
```

---

### 2. **Alternative: Form Backend Services**
If you prefer not to host code:
- Use **Formspree**, **FormSubmit**, or **Basin**.
- Combine with **Zapier** or **Make (Integromat)** for free email routing to multiple recipients.

---

## Final Recommendation
- ✅ Keep Zoho Mail as your inbox.
- ✅ Use **Mailgun** for sending form submissions.
- ✅ Route messages to you and your co-founder via backend or automation tool.
