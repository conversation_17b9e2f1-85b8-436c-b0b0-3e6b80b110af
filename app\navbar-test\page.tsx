"use client";

import React from 'react';
import { NavbarMenuDemo } from '@/components/ui/navbar-menu-demo';

export default function NavbarTestPage() {
  return (
    <div className="min-h-screen pt-28 pb-20 bg-gradient-to-b from-purple-950 via-purple-900/95 to-purple-950 overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
        <h1 className="text-4xl font-bold text-white mb-8 text-center">Navbar Menu Test</h1>
        <p className="text-xl text-white/80 mb-12 text-center">Testing the new dropdown animation</p>
        
        <div className="relative h-[300px] w-full bg-purple-900/30 rounded-xl border border-purple-800/30 flex items-center justify-center">
          <NavbarMenuDemo />
        </div>
      </div>
    </div>
  );
}
