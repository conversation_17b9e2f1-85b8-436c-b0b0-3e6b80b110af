import React from 'react';

interface CheckmarkIconProps extends React.SVGProps<SVGSVGElement> {
  strokeColor?: string;
}

export const CheckmarkIcon: React.FC<CheckmarkIconProps> = ({ strokeColor = "#ffffff", ...props }) => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M20 6L9 17L4 12"
        stroke={strokeColor}
        strokeWidth="2.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};