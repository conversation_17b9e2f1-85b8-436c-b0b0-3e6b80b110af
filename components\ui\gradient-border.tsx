"use client"

import React from "react"
import { motion } from "framer-motion"

interface GradientBorderProps {
  width: number
  height?: number
  baseColor?: string
  gradientColors?: [string, string, string]
  animationDuration?: number
  strokeWidth?: number
}

export const GradientBorder: React.FC<GradientBorderProps> = ({
  width,
  height = 2,
  baseColor = "#E5E7EB",
  gradientColors = ["#9E00FF", "#E879F9", "#9E00FF"],
  animationDuration = 3, // Slower default animation
  strokeWidth = 4,
}) => {
  // Ensure width is a valid number
  const safeWidth = typeof width === 'number' && !isNaN(width) ? width : 1000;
  const gradientId = `border-pulse-${Math.random().toString(36).substr(2, 9)}`
  const path = `M0,${height / 2} L${safeWidth},${height / 2}`

  return (
    <div className="w-full" style={{ height }}>
      <svg
        width="100%"
        height={height}
        viewBox={`0 0 ${safeWidth} ${height}`}
        fill="none"
        preserveAspectRatio="none"
      >
        <path
          d={path}
          stroke={`url(#${gradientId})`}
          strokeLinecap="round"
          strokeWidth={strokeWidth}
        />
        <defs>
          <motion.linearGradient
            animate={{
              x1: [0, safeWidth * 1.5],
              x2: [0, safeWidth * 0.75],
            }}
            transition={{
              duration: animationDuration,
              repeat: Infinity,
              ease: "linear"
            }}
            id={gradientId}
            gradientUnits="userSpaceOnUse"
          >
            <stop stopColor={gradientColors[0]} stopOpacity="0" />
            <stop stopColor={gradientColors[1]} />
            <stop offset="1" stopColor={gradientColors[2]} stopOpacity="0" />
          </motion.linearGradient>
        </defs>
      </svg>
    </div>
  )
}
