"use client";

import * as React from "react";
import { cn } from "@/lib/utils"; // Adjusted path
import Image from "next/image";
import {
  Carousel,
  CarouselApi,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel"; // Adjusted path
import { Building2, User } from "lucide-react"; // Using placeholders

interface Testimonial {
  company: string;
  avatar: string | React.ElementType; // Allow component type for placeholder
  name: string;
  role: string;
  review: string;
  isPlaceholder?: boolean; // Flag for placeholder rendering
  website?: string; // Added website field
}

interface TestimonialCarouselProps extends React.HTMLAttributes<HTMLDivElement> {
  testimonials: Testimonial[];
  // Removed companyLogoPath and avatarPath as we handle placeholders differently
}

export const TestimonialCarousel = React.forwardRef<HTMLDivElement, TestimonialCarouselProps>(
  ({ className, testimonials, ...props }, ref) => {
    const [api, setApi] = React.useState<CarouselApi>();
    const [current, setCurrent] = React.useState(0);

    React.useEffect(() => {
      if (!api) return;
      api.on("select", () => {
        setCurrent(api.selectedScrollSnap());
      });
    }, [api]);

    return (
      <div ref={ref} className={cn("py-16", className)} {...props}>
        <Carousel
          setApi={setApi}
          className="max-w-screen-xl mx-auto px-4 lg:px-8"
          opts={{
            loop: testimonials.length > 1, // Loop only if more than one item
          }}
        >
          <CarouselContent>
            {testimonials.map((testimonial, index) => (
              <CarouselItem
                key={index} // Use index as key since company might not be unique if placeholders are used heavily
                className="flex flex-col items-center cursor-grab"
              >
                {/* Company Logo */}
                <div className="mb-7 relative h-8 w-32 flex items-center justify-center text-muted-foreground">
                  {typeof testimonial.company === 'string' && !testimonial.isPlaceholder ? (
                     // Basic text display for company name if no logo/placeholder logic needed yet
                     <span className="font-semibold">{testimonial.company}</span>
                  ) : (
                     // Placeholder Icon for company logo
                     <Building2 className="h-8 w-8" />
                  )}
                  {/* Original Image logic commented out for placeholder focus
                  <Image
                    src={`${companyLogoPath}${testimonial.company}.svg`}
                    alt={`${testimonial.company} logo`}
                    fill
                    className="object-contain"
                    draggable={false}
                  />
                   */}
                </div>
                {/* Review */}
                <p className="max-w-xl text-balance text-center text-xl sm:text-2xl text-foreground">
                  {testimonial.review}
                </p>
                 {/* Name & Role */}
                <h5 className="mt-5 font-medium text-muted-foreground">
                  {testimonial.name}
                </h5>
                <h5 className="mt-1.5 font-medium text-foreground/40">
                  {testimonial.role}
                </h5>
                 {/* Website Link (Optional) */}
                 {testimonial.website && (
                  <a
                    href={testimonial.website}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="mt-1.5 text-sm text-primary hover:underline"
                  >
                    Visit Website
                  </a>
                )}
                {/* Avatar */}
                <div className="mt-5 relative size-12 rounded-full overflow-hidden bg-muted flex items-center justify-center">
                  {typeof testimonial.avatar === 'string' && !testimonial.isPlaceholder ? (
                    <Image
                      src={testimonial.avatar} // Assuming avatar is a full URL if string
                      alt={testimonial.name}
                      fill
                      className="object-cover"
                    />
                  ) : (
                     // Placeholder Icon for avatar
                     <User className="h-8 w-8 text-muted-foreground" />
                  )}
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
        </Carousel>

        {/* Dots Navigation (only show if more than one testimonial) */}
        {testimonials.length > 1 && (
          <div className="mt-6 text-center">
            <div className="flex items-center justify-center gap-2">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  className={cn(
                    "size-1.5 rounded-full transition-all",
                    index === current ? "bg-primary" : "bg-primary/35"
                  )}
                  onClick={() => api?.scrollTo(index)}
                  aria-label={`Go to slide ${index + 1}`}
                />
              ))}
            </div>
          </div>
        )}
      </div>
    );
  }
);

TestimonialCarousel.displayName = "TestimonialCarousel";
