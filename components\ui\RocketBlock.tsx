import { Rocket } from "lucide-react";
import { GradientTracing } from "@/components/ui/gradient-tracing";

export function RocketBlock() {
  return (
    <div className="relative">
      <div className="absolute inset-0 bg-gradient-to-r from-pink-500/20 to-purple-500/20 rounded-lg blur-3xl"></div>
      <div className="relative bg-purple-900/50 rounded-lg border border-purple-700/50 backdrop-blur-sm p-8 hover:scale-[1.02] transition-transform duration-300 flex items-center justify-center">
        <GradientTracing
          width={300}
          height={300}
          path="M150,150 m0,-120 a120,120 0 1,1 -0.1,0 z"
          gradientColors={["#FF0080", "#7928CA", "#FF0080"]}
          animationDuration={3}
          strokeWidth={3}
        />
        <Rocket
          className="absolute text-white w-16 h-16 transform -rotate-45"
          style={{
            filter: "drop-shadow(0 0 20px rgba(255,255,255,0.3))",
          }}
        />
      </div>
    </div>
  );
}